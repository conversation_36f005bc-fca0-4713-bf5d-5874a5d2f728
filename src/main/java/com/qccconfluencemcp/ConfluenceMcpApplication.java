package com.qccconfluencemcp;

import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.MethodToolCallbackProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

/**
 * Confluence MCP服务器主应用类
 * 
 * 基于Spring AI MCP Server Boot Starter构建的Confluence集成服务器
 * 提供Confluence文档搜索、获取、创建和更新等功能
 */
@SpringBootApplication
public class ConfluenceMcpApplication {

    public static void main(String[] args) {
        SpringApplication.run(ConfluenceMcpApplication.class, args);
    }

    /**
     * 注册Confluence工具到MCP服务器
     * 
     * @param confluenceService Confluence服务实例
     * @return ToolCallbackProvider 工具回调提供者
     */
    @Bean
    public ToolCallbackProvider confluenceTools(ConfluenceService confluenceService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(confluenceService)
                .build();
    }
}
