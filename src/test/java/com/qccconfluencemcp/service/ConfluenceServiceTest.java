package com.qccconfluencemcp.service;

import com.qccconfluencemcp.config.ConfluenceProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConfluenceService测试类
 */
@ExtendWith(MockitoExtension.class)
class ConfluenceServiceTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfluenceServiceTest.class);
    
    private ConfluenceService confluenceService;
    private ConfluenceProperties properties;
    
    @BeforeEach
    void setUp() {
        properties = new ConfluenceProperties();
        properties.setBaseUrl("https://doc.greatld.com");
        properties.setUsername("test_user");
        properties.setPassword("test_password");
        properties.setConnectTimeout(5000);
        properties.setReadTimeout(10000);
        
        confluenceService = new ConfluenceService(properties);
    }
    
    @Test
    void testServiceInitialization() {
        assertNotNull(confluenceService);
        logger.info("ConfluenceService初始化测试通过");
    }
    
    @Test
    void testGetConfluencePageWithInvalidParameters() {
        // 测试无效参数
        String result = confluenceService.getConfluencePage(null, null);
        assertTrue(result.contains("错误：必须提供pageId或pageUrl参数"));
        logger.info("无效参数测试通过");
    }
    
    @Test
    void testSearchConfluenceWithEmptyQuery() {
        // 测试空查询
        String result = confluenceService.searchConfluence("", 10, 0);
        assertTrue(result.contains("错误：搜索关键词不能为空"));
        logger.info("空查询测试通过");
    }
    
    @Test
    void testSearchConfluenceWithNullQuery() {
        // 测试null查询
        String result = confluenceService.searchConfluence(null, 10, 0);
        assertTrue(result.contains("错误：搜索关键词不能为空"));
        logger.info("null查询测试通过");
    }
}
